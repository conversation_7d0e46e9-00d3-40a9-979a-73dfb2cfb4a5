<?php

namespace App\Jobs;

use App\Models\FacebookGroup;
use App\Models\UserPost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CrawGroupDone implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();

        $groupId = @$payload['group_id'];
        if (empty($groupId)) {
            return;
        }
        $group = FacebookGroup::where('page_id',$groupId)->first();
        if (empty($group)) {
            return;
        };

        $latestPost = UserPost::where('fb_post_id','like',"{$groupId}_%")
            ->orderByDesc(\DB::raw("JSON_UNQUOTE(JSON_EXTRACT(message, '$._values.fb_created'))"))
            ->first();

        if (empty($latestPost)) {
            return;
        }
        $group->lastest_post = explode('_',$latestPost["fb_post_id"])[1];
        $group->save();
    }
}


