<?php

namespace App\Jobs;

use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateCursorGroupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();
        $pageId = $payload['page_id'];
        $cursor = @$payload['cursor'] ?? null;

        $page = FacebookGroup::where('page_id', $pageId)->first();
        if (!$page) {
            return;
        }
        $page->cursor_fb = $cursor;
        $page->save();
    }
}

