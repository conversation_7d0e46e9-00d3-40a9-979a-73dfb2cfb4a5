<?php

namespace App\Jobs\Redis;

use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use App\Service\ApiReactionService;
use App\Service\TelegramService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class ReactThreadsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    CONST INSTAGRAM_ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';

    public function __construct(
        public ReactionBatch $batch,
        public array         $links
    )
    {
    }

    public function handle(): void
    {
        try {
            if (empty($this->links)) {
                return;
            }

            $links = [];
            $linksError = [];
            $ids = [];

            $reactionData = [
                'count_like' => 0,
                'count_share' => 0,
                'count_comment' => 0,
                'count_view' => 0
            ];
            $now = Carbon::now();

            $time = [
                'created_at' => $now,
                'updated_at' => $now,
                'batch_id' => $this->batch->id
            ];
            foreach ($this->links as $link) {
                $id = $this->post_id_to_pk($this->getThreadsPostId($link['url']));
                if ($id) {
                    $ids[] = $id;
                    $links[] = [
                        'post_id' => $id,
                        'url' => $link['url'],
                        'sort' => $link['sort'],
                        ...$time,
                    ];
                    continue;
                }
                $linksError[] = [
                    'post_id' => null,
                    'url' => $link['url'],
                    'sort' => $link['sort'],
                    'data' => json_encode($reactionData),
                    ...$time
                ];

            }


            /** @var ApiReactionService $api */
            $api = app(ApiReactionService::class);

            $attempts = 0;
            $maxAttempts = 3;
            $data = [];
            while ($attempts < $maxAttempts) {
                $response = $api->threads($ids);
                $data = collect($response)->keyBy('pk')->toArray();
                if (!empty($data)) {
                    break;
                }
                $attempts++;
            }

            foreach ($links as $idx => $link) {
                $id = $link['post_id'];
                if (!empty($data[$id])) {
                    $links[$idx]['data'] = json_encode([
                        'count_like' => $data[$id]['like_count'] ?? 0,
                        'count_share' => 0,
                        'count_comment' => @$data[$id]['text_post_app_info']['direct_reply_count'] ?? 0,
                        'count_view' => 0
                    ]);
                    continue;
                }
                $linksError[] = [
                    ...$link,
                    'data' => json_encode($reactionData),
                    ...$time
                ];
                unset($links[$idx]);
            }
            if (!empty($linksError)) {
                ReactionPost::insert($linksError);
            }
            if (!empty($links)) {
                ReactionPost::insert(array_values($links));
            }
        }catch (\Exception $exception){
            TelegramService::sendMessage($exception->getMessage() . " line: " . $exception->getLine());
        }
    }
    function getThreadsPostId($url) {
        if (!str_contains($url,'threads')){
            return $url;
        }
        $pattern = '/\/post\/([^\/]+)/';
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1]; // Post ID
        }
        return null; // Không tìm thấy
    }

    function pk_to_post_id($pk) {
        $post_id = '';
        while ($pk > 0) {
            $remainder = $pk % 64;
            $pk = intval($pk / 64);
            $post_id = self::INSTAGRAM_ALPHABET[$remainder] . $post_id;
        }
        return $post_id;
    }

    function post_id_to_pk($post_id) {
        if (is_numeric($post_id)) {
            return $post_id;
        }
        $pk = 0;
        $length = strlen($post_id);
        for ($i = 0; $i < $length; $i++) {
            $char = $post_id[$i];
            $pk = $pk * 64 + strpos(self::INSTAGRAM_ALPHABET, $char);
        }
        return (string)$pk;
    }
}

