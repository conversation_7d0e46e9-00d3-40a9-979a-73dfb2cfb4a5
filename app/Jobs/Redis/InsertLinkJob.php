<?php

namespace App\Jobs\Redis;

use App\Filament\Resources\ReactionBatchResource;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use App\Service\ApiReactionService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InsertLinkJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public ReactionBatch $batch,
        public string        $link,
        public               $index
    )
    {
    }

    public function handle(): void
    {
        if (empty($this->link)) {
            return;
        }

        $postId = null;
        $reactionData = $this->getDefaultReactionData();
        $api = app(ApiReactionService::class);

        switch ($this->batch->type) {
            case ReactionBatchResource::TIKTOK:
                $postId = $this->extractTiktokVideoId($this->link);
                if ($postId) {
                    $result = $this->retry(fn() => $api->tiktok($postId));
                    $reactionData = [
                        'count_like' => $result['like_count'] ?? 0,
                        'count_share' => $result['share_count'] ?? 0,
                        'count_comment' => $result['comment_count'] ?? 0,
                        'count_view' => $result['view_count'] ?? 0,
                    ];
                }
                break;

            case ReactionBatchResource::INSTAGRAM:
                $postId = $this->extractInstagramId($this->link);
                if ($postId) {
                    $result = $this->retry(fn() => $api->instagram($postId));
                    $reactionData = [
                        'count_like' => $result['like'] ?? 0,
                        'count_share' => 0,
                        'count_comment' => $result['comment'] ?? 0,
                        'count_view' => 0,
                    ];
                }
                break;
        }

        ReactionPost::create([
            'status' => 1,
            'url' => $this->link,
            'data' => $reactionData,
            'sort' => $this->index,
            'batch_id' => $this->batch->id,
            'post_id' => $postId
        ]);
    }

    private function getDefaultReactionData(): array
    {
        return [
            'count_like' => 0,
            'count_share' => 0,
            'count_comment' => 0,
            'count_view' => 0,
        ];
    }

    private function retry(callable $callback, int $maxAttempts = 3): array
    {
        $attempts = 0;
        do {
            $result = $callback();
            if (!empty($result)) {
                return $result;
            }
            $attempts++;
        } while ($attempts < $maxAttempts);

        return [];
    }

    private function extractTiktokVideoId(string $link): ?string
    {
        if (!str_contains($link,'tiktok')){
            return $link;
        }
        $re = '/(@[a-zA-z0-9]*|.*)(\/.*\/|trending.?shareId=)([\d]*)/m';
        preg_match_all($re, $link, $matches, PREG_SET_ORDER, 0);
        return !empty($matches) && isset($matches[0][3]) ? $matches[0][3] : null;
    }

    private function extractInstagramId(string $link): ?string
    {
        if (!str_contains($link,'instagram')){
            return $link;
        }
        $re = '/(?:https?:\/\/)?(?:www.)?instagram.com\/?([a-zA-Z0-9\.\_\-]+)?\/([p]+)?([reel]+)?([tv]+)?([stories]+)?\/([a-zA-Z0-9\-\_\.]+)\/?([0-9]+)?/m';
        preg_match_all($re, $link, $matches, PREG_SET_ORDER, 0);
        return !empty($matches) && isset($matches[0][6]) ? $matches[0][6] : null;
    }
}
