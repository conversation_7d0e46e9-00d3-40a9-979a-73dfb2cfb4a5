<?php

namespace App\Jobs\Redis;

use App\Models\FacebookPage;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateReactionJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public               $ids,
        public ReactionBatch $batch,
        public               $typeGet = ['count_like', 'count_share', 'count_comment']
    )
    {
    }

    public function handle(): void
    {
        $batch = $this->batch;
        $position = 0;
        $batchId = $batch->id;
        $chunked = collect($this->ids)->chunk(500);
        foreach ($chunked as $chunk) {
            $dataToInsert = [];
            foreach ($chunk as $id) {
                $dataToInsert[] = [
                    'status' => 0,
                    'url' => "www.facebook.com/{$id}",
                    'sort' => $position++,
                    'batch_id' => $batchId,
                    'post_id' => $id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            ReactionPost::insert($dataToInsert);
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_reactions', json_encode([
                'batch_id' => $batchId,
                'ids' => array_values($chunk->toArray()),
                'type_get' => $this->typeGet
            ]));
        }

    }
}
