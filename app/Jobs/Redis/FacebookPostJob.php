<?php

namespace App\Jobs\Redis;

use App\Filament\Resources\ReactionBatchResource;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use App\Service\ApiReactionService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FacebookPostJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public ReactionBatch $batch,
        public               $links,
    )
    {
    }

    public function handle(): void
    {
        $batch = $this->batch;
        if (is_string($this->links)) {
            $links = explode("\n", trim($this->links));
            $links = collect($links)
                ->map(function ($line, $index) {
                    [$id, $type] = array_pad(preg_split('/\s+/', trim($line)), 2, null);
                    $type = trim($type);
                    return [
                        'id' => trim($id),
                        'type' => !empty($type) ? $type : 'Comment',
                        'position' => $index,
                    ];
                })
                ->filter(fn($row) => !empty($row['id']) && !empty($row['type']))
                ->values();
            $posts = $links->where('type', 'Post')->values();
            $comments = $links->where('type', 'Comment')->values();

            $batch->total = $links->count();
            $batch->save();
            $posts->chunk(1000)->each(function ($chunk) use ($batch) {
                FacebookPostJob::dispatch($batch, $chunk->toArray())
                    ->onConnection("redis")
                    ->onQueue('default');;
            });
            $this->insertComment($comments);
            return;
        }

        $items = collect($this->links);
        $items->chunk(20)->each(function ($smallChunk) use ($batch) {
            ProcessFacebookReactions::dispatch($smallChunk->toArray(),$batch)
                ->onConnection("redis")
                ->onQueue('default');
        });
    }
    private function insertComment($data)
    {
        $data = is_array($data) ? collect($data) : $data;

        $formatted = $data->map(function ($item) {
            return [
                'status' => 1,
                'url' => $item['id'],
                'data' => json_encode([
                    'count_like' => 0,
                    'count_share' => 0,
                    'count_comment' => 0,
                    'count_view' => 0,
                ]),
                'sort' => $item['position'],
                'batch_id' => $this->batch->id,
                'post_id' => $item['id'],
            ];
        });
        $formatted->chunk(50)->each(function ($chunk) {
            ReactionPost::insert($chunk->toArray());
        });
    }
}
