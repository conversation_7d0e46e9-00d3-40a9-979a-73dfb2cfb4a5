<?php

namespace App\Jobs\Redis;

use App\Filament\Resources\ReactionBatchResource;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use App\Service\ApiReactionService;
use App\Service\TelegramService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class ReactionYoutubeJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public ReactionBatch $batch,
        public array         $links
    )
    {
    }

    public function handle(): void
    {
        try {
            if (empty($this->links)) {
                return;
            }

            $links = [];
            $linksError = [];
            $ids = [];

            $reactionData = [
                'count_like' => 0,
                'count_share' => 0,
                'count_comment' => 0,
                'count_view' => 0
            ];
            $now = Carbon::now();

            $time = [
                'created_at' => $now,
                'updated_at' => $now,
                'batch_id' => $this->batch->id
            ];
            foreach ($this->links as $link) {
                $id = $this->extractId($link['url']);
                if ($id) {
                    $ids[] = $id;
                    $links[] = [
                        'post_id' => $id,
                        'url' => $link['url'],
                        'sort' => $link['sort'],
                        ...$time,
                    ];
                    continue;
                }
                $linksError[] = [
                    'post_id' => null,
                    'url' => $link['url'],
                    'sort' => $link['sort'],
                    'data' => json_encode($reactionData),
                    ...$time
                ];

            }

            $ids = array_unique($ids);

            /** @var ApiReactionService $api */
            $api = app(ApiReactionService::class);

            $attempts = 0;
            $maxAttempts = 3;
            $data = [];

            while ($attempts < $maxAttempts) {
                $response = $api->youtube($ids);
                $data = collect($response)->keyBy('id')->toArray();
                if (!empty($data)) {
                    break;
                }
                $attempts++;
            }

            foreach ($links as $idx => $link) {
                $id = $link['post_id'];
                if (!empty($data[$id])) {
                    $statistics = $data[$id]['statistics'];
                    $links[$idx]['data'] = json_encode([
                        'count_like' => $statistics['likeCount'] ?? 0,
                        'count_share' => 0,
                        'count_comment' => $statistics['commentCount'] ?? 0,
                        'count_view' => $statistics['viewCount'] ?? 0
                    ]);
                    continue;
                }
                $linksError[] = [
                    ...$link,
                    'data' => json_encode($reactionData),
                    ...$time
                ];
                unset($links[$idx]);
            }
            if (!empty($linksError)) {
                ReactionPost::insert($linksError);
            }
            if (!empty($links)) {
                ReactionPost::insert(array_values($links));
            }
        }catch (\Exception $exception){
            TelegramService::sendMessage($exception->getMessage() . " line: " . $exception->getLine());
        }
    }

    private function extractId(string $link): ?string
    {
        if (!str_contains($link,'youtu')){
            return $link;
        }
        preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $link, $match);
        return @$match[1] ?? null;
    }
}
