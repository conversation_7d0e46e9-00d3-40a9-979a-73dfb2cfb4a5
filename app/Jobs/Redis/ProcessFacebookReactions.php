<?php

namespace App\Jobs\Redis;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use App\Filament\Resources\ReactionBatchResource;
use App\Models\ReactionBatch;
use App\Models\ReactionPost;
use App\Service\ApiReactionService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessFacebookReactions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public array $ids, public $batch)
    {
    }

    public function handle()
    {
        try {
            /** @var ApiReactionService $api */
            $api = app()->make(ApiReactionService::class);

            $ids = collect($this->ids)->pluck('id')->toArray();

            if (count($ids) == 1) {
                $id = $this->ids[0]['id'];
                $position = $this->ids[0]['position'];
                $result = $this->retry(fn() => $api->facebookReactionSingle($id));
                $data = $result
                    ? array_merge(
                        $this->getDefaultReactionData(),
                        $this->getData($result),
                    )
                    : $this->getDefaultReactionData();

                ReactionPost::create([
                    'status' => 1,
                    'url' => $id,
                    'data' => $data,
                    'sort' => $position,
                    'batch_id' => $this->batch->id,
                    'post_id' => $id,
                ]);
                return;
            }

            $result = $this->retry(fn() => $api->facebookReactionIds($ids));
            $dataInsert = [];
            foreach ($this->ids as $item){
                $default = [
                    'status' => 1,
                    'url' => $item['id'],
                    'sort' => $item['position'],
                    'batch_id' => $this->batch->id,
                    'post_id' => $item['id'],

                ];
                if (empty($result[$item['id']])) {
                    $dataInsert[] = [
                        ...$default,
                        'data' => json_encode($this->getDefaultReactionData())
                    ];
                    continue;
                }
                $dataInsert[] = [
                    'data' => json_encode(array_merge(
                        $this->getDefaultReactionData(),
                        $this->getData($result[$item['id']]),
                    )),
                    ...$default

                ];
            }
            ReactionPost::insert($dataInsert);


        } catch (\Throwable $e) {
            if (count($this->ids) <= 5) {
                foreach ($this->ids as $id) {
                    ProcessFacebookReactions::dispatch([$id], $this->batch)
                        ->onConnection("redis")
                        ->onQueue('default');
                }
                return;
            }
            collect($this->ids)
                ->chunk(ceil(count($this->ids) / 2))
                ->each(fn($chunk) => ProcessFacebookReactions::dispatch($chunk->toArray(), $this->batch)
                    ->onConnection("redis")
                    ->onQueue('default'));
        }
    }


    private function retry(callable $callback, int $maxAttempts = 3): array
    {
        $attempts = 0;

        do {
            $result = $callback();
            if (empty($result['error'])) {
                return $result;
            }
            // code 368 sleep 5s retry again
            if ($result['code'] == 368) {
                sleep(5);
            } else {
                return $result;
            }

            $attempts++;
        } while ($attempts < $maxAttempts);

        return [];
    }



    private function getDefaultReactionData(): array
    {
        return [
            'count_like' => 0,
            'count_share' => 0,
            'count_comment' => 0,
            'count_view' => 0,
        ];
    }

    private function getData($data)
    {
        return [
            'count_like' => @$data['like']['summary']['total_count'] ?? 0,
            'count_comment' => @$data['comments']['count'] ?? 0,
            'count_share' => @$data['shares']['count'] ?? 0,
        ];
    }
}
