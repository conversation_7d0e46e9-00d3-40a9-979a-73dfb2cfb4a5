<?php

namespace App\Jobs;

use App\Models\Counter;
use App\Models\FacebookUser;
use App\Models\PostFacebookPage;
use App\Models\UserPagePost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InsertUserPagePostJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();
        $data = $payload['data'];
        $cursor = $payload['cursor'] ?? null;
        $currPage = $payload['curr_page'] ?? 1;
        $isFinish = $payload['is_finish'] ?? false;
        $pageId = $payload['page_id'];
        $isError = $payload['is_error'] ?? false;
        $isPrivate = @$payload['is_private'] ?? false;

        $userPage = FacebookUser::query()->where('page_id',$pageId)->first();
        if (empty($userPage)) {
            return;
        }

        if ($isError && $currPage == 1){
            $userPage->time_check = 0;
            $userPage->save();
            return;
        }

        if ($currPage == 1 && empty($data)){
            $userPage->is_private = 1;
            $userPage->cursor = null;
            $userPage->curr_page = 0;
            $userPage->save();
            return;
        }

        $userPage->is_finish = $isFinish;

        $userPage->cursor = $isFinish ? null : $cursor;
        $userPage->curr_page = $isFinish ? 0 : $currPage;
        $userPage->time_check = time();
        if ($isFinish && $userPage->is_first){
            $userPage->is_first = 0;
        }
        if ($isError){
            $userPage->time_check = 0;
        }
        $userPage->save();
        $dataInsert = [];
        foreach ($data as $p) {

            $dataInsert[] = [
                'fb_post_id' => $p["fb_post_id"],
                'message' => $p['message'],
                'created_time' => time(),
            ];
        }

        try {
            Counter::create([
                'count' => count($dataInsert),
                'type' => 1,
            ]);
            PostFacebookPage::insertOrIgnore($dataInsert);
        } catch (\Exception $e) {

        }
    }
}

