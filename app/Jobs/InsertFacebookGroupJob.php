<?php

namespace App\Jobs;

use App\Models\Counter;
use App\Models\FacebookGroup;
use App\Models\UserPost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InsertFacebookGroupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();
        $items = $payload['items'];
        $dataInsert = [];
        foreach ($items as $p) {
            $dataInsert[] = [
                'fb_post_id' => $p["fb_post_id"],
                'message' => $p['message'],
                'created_time' => time(),
            ];
        }
        $cursor = @$payload['cursor'];
        $nextCursor = @$payload['next_cursor'];
        $groupId = $payload['page_id'];
        $end = @$payload['end'] ?? false;

        $group = FacebookGroup::where('page_id', $groupId)->first();
        if (empty($group)) {
            return;
        }
        if ($end){
            $group->is_first = false;
        }
        try {
            Counter::create([
                'count' => count($dataInsert),
                'type' => 0,
            ]);
            UserPost::insertOrIgnore($dataInsert);
            if ($cursor && $cursor != $group->cursor_fb){
                $group->cursor_fb = $cursor;
                $group->status = FacebookGroup::STATUS_NEXT;
            }else{
                $group->cursor_fb = null;
                $group->status = 0;
            }
            if ($nextCursor && $nextCursor != $group->next_cursor){
                $group->next_cursor = $nextCursor;
                $group->cursor_count = ($group->cursor_count ?? 0) + 1;
            }

            $group->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}

