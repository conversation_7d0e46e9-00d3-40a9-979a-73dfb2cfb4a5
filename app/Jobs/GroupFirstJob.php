<?php

namespace App\Jobs;

use App\Models\FacebookGroup;
use App\Models\UserPost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Group<PERSON>irstJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();
        $pageId = $payload['page_id'];
        $status  = $payload['status'];
        $cursor = @$payload['cursor'] ?? '';
        $group  =  FacebookGroup::where('page_id', $pageId)->first();
        if (!$group) {
            return;
        }
        $updates = [
            'cursor_fb' => $status ? $cursor : null,
            'is_first'  => (bool) $status,
            'cursor_count' => 0,
            'next_cursor' => ''
        ];
        $group->update($updates);

    }
}


