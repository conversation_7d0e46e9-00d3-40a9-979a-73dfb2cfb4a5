<?php

namespace App\Jobs;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelQueueRabbitMQ\Queue\Jobs\RabbitMQJob as BaseJob;

class RabbitMQJob extends BaseJob
{
    private $data;
    /**
     * Fire the job.
     *
     * @return void
     */
    public function fire()
    {
        $payload = $this->payload();
        $this->data = $payload['data'] ?? [];

        $class = $payload['job'];
        $method = 'handle';
        ($this->instance = $this->resolve($class))->{$method}($this, $payload);
        $this->delete();
    }
    public function getData()
    {
        return $this->data;
    }
    public function getName()
    {
        return "";
    }
}
