<?php

namespace App\Jobs;

use App\Models\Counter;
use App\Models\FacebookGroup;
use App\Models\PostFacebookPage;
use App\Models\UserPost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InsertPostPageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();
        $items = $payload['items'];
        $dataInsert = [];
        foreach ($items as $p) {
            $dataInsert[] = [
                'fb_post_id' => $p["fb_post_id"],
                'message' => $p['message'],
                'created_time' => time(),
            ];
        }

        try {
            Counter::create([
                'count' => count($dataInsert),
                'type' => 1,
            ]);
            PostFacebookPage::insert($dataInsert);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error($e->getMessage());
        }
    }
}

