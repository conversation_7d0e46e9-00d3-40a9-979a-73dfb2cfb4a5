<?php

namespace App\Jobs;

use App\Models\FacebookGroup;
use App\Models\FacebookPost;
use App\Models\FbComments;
use App\Models\UserPost;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InsertCommentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    public function handle(RabbitMQJob $job)
    {
        $payload = $job->getData();

        $postId = @$payload['post_id'];
        $data = @$payload['data'];
        $currPage = $payload['curr_page'] ?? 1;
        $isFinish = $payload['is_finish'] ?? false;
        $cursor = $payload['cursor'] ?? null;

        $isError = $payload['is_error'] ?? false;
        $post = FacebookPost::query()->find($postId);
        if (empty($post)){
            return;
        }
        if ($isError && $currPage == 1){
            $post->time_check = 0;
            $post->save();
            return;
        }
        if ($currPage == 1 && empty($data)){
            $post->cursor = null;
            $post->curr_page = 0;
            $post->is_first = 0;
            $post->save();
            return;
        }

        $post->cursor = $isFinish ? null : $cursor;
        $post->curr_page = $isFinish ? 0 : $currPage;
        $post->time_check = time();
        if ($isFinish && $post->is_first){
            $post->is_first = 0;
        }
        if ($isError){
            $post->time_check = 0;
        }
        $post->save();

        $dataInsert = [];
        foreach ($data as $p) {
            $userId = str_contains($p['user_id'],'pfbid') ? null : $p['user_id'];
            $dataInsert[] = [
                'fb_post_id' => $post->id,
                'data' => json_encode($p),
                'comment_id' => $p['cmt_id'],
                'created_time' => $p['created_time'] ?? time(),
                'user_id' => $userId
            ];
        }
        FbComments::insertOrIgnore($dataInsert);


    }
}



