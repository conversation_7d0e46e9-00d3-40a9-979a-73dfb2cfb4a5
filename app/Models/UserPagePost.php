<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserPagePost extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'user_page_posts';

    protected $fillable = [
        'fb_post_id',
        'messages',
        'created_time',
        'indexed',
        'mysql_indexed'
    ];
    public $timestamps = false;
    protected $casts = [
        'messages' => 'array'
    ];

}
