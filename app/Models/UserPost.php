<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserPost extends Model
{
    protected $table = 'users_posts_raw';
    protected $connection = 'mysql3';

    protected $fillable = [
        'fb_post_id',
        'message',
        'created_time',
        'indexed',
        'mysql_indexed',
//        'page_id'
    ];
    protected $casts = [
        'message' => 'array',
    ];
    public $timestamps = false;
}
