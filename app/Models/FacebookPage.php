<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FacebookPage extends Model
{
    protected $table = 'fb_page';
    protected $connection = 'mysql2';

    protected $fillable = [
        'page_id',
        'page_name',
        'page_link',
        'page_type',
        'is_priority',
        'status_code',
        'created',
        'status',
        'branch',
        'page_name',
        'sub_branch',
        'page_like_count',
        'is_code',
        'is_first',
        'lastest_post',
        'filtered_time',
        'group_page',
        'status_get_posts_no_auth',
        'cursor_fb',
        'type_scan',
        'time_post_new',
        'updated_at',
        'status_page',
        'user_page_id',
        'filter_time',
        'crawl_time',
        'count_post',
        'type'
    ];
    public $timestamps = false;
}
