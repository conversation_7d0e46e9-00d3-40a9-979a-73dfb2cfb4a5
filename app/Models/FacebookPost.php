<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FacebookPost extends Model
{
    protected $connection = 'mysql';
    protected $table = 'fb_posts';
    protected $fillable = [
        'post_id',
        'type',
        'status',
        'cursor',
        'is_first',
        'type_get',
        'curr_page',
        'time_check'
    ];
    protected $casts = [
        'status' => 'boolean',
        'is_first' => 'boolean'
    ];

    public function comments()
    {
        return $this->hasMany(FbComments::class, 'fb_post_id', 'id');
    }
}
