<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FacebookGroup extends Model
{
    protected $table = 'fb_groups';
    protected $connection = 'mysql2';
    const STATUS_NEXT = 1;
    protected $fillable = [
        'page_id',
        'page_link',
        'page_type',
        'is_priority',
        'status_code',
        'created',
        'status',
        'branch',
        'page_name',
        'sub_branch',
        'page_like_count',
        'is_code',
        'is_first',
        'lastest_post',
        'filtered_time',
        'group_page',
        'status_get_posts_no_auth',
        'cursor_fb',
        'type_scan',
        'time_post_new',
        'filter_time',
        'crawl_time',
        'status_get_info',
        'number_of_posts_in_last_day',
        'number_of_posts_in_last_month',
        'scanning_frequency',
        'next_time_crawl',
        'count_post',
        'number_posts_last_day',
        'sync_to_check_number_posts',
        'type',
        'cursor_count',
        'next_cursor'
    ];


    public function latestPost()
    {
        return $this->hasOne(UserPost::class, 'page_id', 'page_id');
    }
}
