<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MonitazVia extends Model
{
    protected $table = 'monitaz_manager_via';
    protected $connection = 'mysql4';
    protected $fillable = [
        'uid',
        'pass',
        '2fa',
        'email',
        'password_email',
        'name_bm',
        'note',
        'type_bm',
        'status_via',
        'status_token',
        'token',
        'in_bm',
        'email_protect',
        'birthday',
        'new_password_email',
        'note_phone_number_email',
        'note_phone_number_via',
        'live_in',
        'is_pending'
    ];
    protected $casts = [
        'is_pending' => 'boolean'
    ];

}
