<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FacebookUser extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'fb_page_user';
    protected $fillable = [
        'page_id',
        'page_link',
        'page_type',
        'is_priority',
        'status_code',
        'created',
        'status',
        'branch',
        'page_name',
        'sub_branch',
        'page_like_count',
        'is_code',
        'is_first',
        'lastest_post',
        'filtered_time',
        'group_page',
        'is_finish',
        'cursor',
        'curr_page',
//        'is_have_data',
        'time_check',
        'is_private'
    ];
    public $timestamps = false;

    protected $casts = [
        'is_have_data' => 'integer'
    ];
}
