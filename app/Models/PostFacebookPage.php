<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PostFacebookPage extends Model
{
    protected $table = 'users_posts_raw_page';
    protected $connection = 'mysql3';
    protected $fillable = [
        'fb_post_id',
        'message',
        'created_time',
        'indexed',
        'mysql_indexed'
    ];
    public $timestamps = false;
    protected $casts = [
        'message' => 'array'
    ];
}
