<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ReactionBatch extends Model
{
    protected $connection = 'mysql';
    protected $table = 'reaction_batchs';

    protected $fillable = [
        'name',
        'type',
        'total',
        'batch_id',
        'callback_url'
    ];

    public function posts()
    {
        return $this->hasMany(ReactionPost::class, 'batch_id', 'id');
    }
}
