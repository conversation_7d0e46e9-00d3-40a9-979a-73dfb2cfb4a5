<?php

namespace App\Filament\Exports;

use App\Models\ReactionPost;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
class ReactionPostExporter extends Exporter
{
    protected static ?string $model = ReactionPost::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('post_id')->label("Post Id"),
            ExportColumn::make('url')->label("Link"),
            ExportColumn::make('data.count_like')->label("Likes"),
            ExportColumn::make('data.count_share')->label("Share"),
            ExportColumn::make('data.count_comment')->label("Comment"),
            ExportColumn::make('data.count_view')->label("View"),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {

        $body = "Export đã hoàn thành " . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
    public function getJobConnection(): ?string
    {
        return 'sync'; // TODO: Change the autogenerated stub
    }

}
