<?php

namespace App\Filament\Exports;

use App\Models\FbComments;
use App\Models\ReactionPost;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class CommentExporter extends Exporter
{
    protected static ?string $model = FbComments::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('comment_id')->label("Comment Id"),
            ExportColumn::make('data.username')->label("Username"),
            ExportColumn::make('data.user_id')->label("User ID"),
            ExportColumn::make('data.user_id')->label("User ID"),
            ExportColumn::make('data.comment')->label("Comment"),

        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {

        $body = "Export đã hoàn thành " . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
    public function getJobConnection(): ?string
    {
        return 'sync'; // TODO: Change the autogenerated stub
    }

}
