<?php

namespace App\Filament\Resources;

use App\Filament\Action\PushJobPageAction;
use App\Filament\Resources\FacebookPageResource\Pages;
use App\Filament\Resources\FacebookPageResource\RelationManagers;
use App\Models\FacebookPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FacebookPageResource extends Resource
{
    protected static ?string $model = FacebookPage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('page_id'),
                Forms\Components\TextInput::make('page_name'),
                Forms\Components\TextInput::make('page_link'),
                Forms\Components\Toggle::make('is_priority'),
                Forms\Components\Toggle::make('is_first'),
                Forms\Components\TextInput::make('cursor_fb'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('page_id')
                ->searchable(),
                Tables\Columns\TextColumn::make('user_page_id')
                ->searchable(),
                Tables\Columns\TextColumn::make('page_name')
                ->searchable(),
                Tables\Columns\TextColumn::make('page_link'),
                Tables\Columns\TextColumn::make('page_type'),
                Tables\Columns\TextColumn::make('is_priority')
                ->sortable(),
                Tables\Columns\TextColumn::make('type_scan'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                PushJobPageAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Action::make('run_all')
                    ->label('Run All')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Xác nhận chạy tất cả')
                    ->modalDescription('Bạn có chắc chắn muốn chạy tất cả không? Hành động này không thể hoàn tác.')
                    ->modalButton('Chạy ngay') // Nút xác nhận
                    ->action(fn() => self::runAllTasks()),
            ])
            ->defaultSort('created','desc');
    }
    public static function runAllTasks()
    {
        $totalWoker = getConfig('total_worker');
        $groups = FacebookPage::query()->whereNotNull('user_page_id')->limit(10000)->get();


        $groupChunks = array_chunk($groups->toArray(), $totalWoker);

        foreach ($groupChunks as $chunk) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_fb_pages_test', json_encode($chunk));
        }


        Notification::make()
            ->title("Success")
            ->body("Run task success")
            ->success()
            ->send();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacebookPages::route('/'),
            'create' => Pages\CreateFacebookPage::route('/create'),
            'edit' => Pages\EditFacebookPage::route('/{record}/edit'),
        ];
    }
}
