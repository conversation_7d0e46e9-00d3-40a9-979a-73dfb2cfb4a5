<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserPagePostResource\Pages;
use App\Filament\Resources\UserPagePostResource\RelationManagers;
use App\Models\UserPagePost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

class UserPagePostResource extends Resource
{
    protected static ?string $model = UserPagePost::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Facebook User Page';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                JsonColumn::make('message')->columnSpan(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('fb_post_id'),
                Tables\Columns\TextColumn::make('created_time')
                    ->formatStateUsing(function ($record){
                        return Carbon::parse($record->created_time)
                            ->timezone('Asia/Ho_Chi_Minh')
                            ->format('Y-m-d H:i:s');
                    }),
                Tables\Columns\TextColumn::make('indexed'),
                Tables\Columns\TextColumn::make('mysql_indexed'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_time');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserPagePosts::route('/'),
            'create' => Pages\CreateUserPagePost::route('/create'),
            'edit' => Pages\EditUserPagePost::route('/{record}/edit'),
        ];
    }
}
