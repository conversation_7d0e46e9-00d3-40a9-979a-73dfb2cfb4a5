<?php

namespace App\Filament\Resources\ReactionBatchResource\Pages;

use App\Filament\Resources\ReactionBatchResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewReactionBatch extends ViewRecord
{
    protected static string $resource = ReactionBatchResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->label("Thông tin")
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label("Tên"),
                        Infolists\Components\TextEntry::make('type')
                            ->formatStateUsing(function ($record) {
                                return ReactionBatchResource::TYPE[$record->type];
                            })
                            ->label("Loại"),
                        Infolists\Components\TextEntry::make('batch_id')
                            ->label("uid"),
                    ])
            ]);
    }
}
