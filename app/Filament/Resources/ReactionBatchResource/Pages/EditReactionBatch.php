<?php

namespace App\Filament\Resources\ReactionBatchResource\Pages;

use App\Filament\Resources\ReactionBatchResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditReactionBatch extends EditRecord
{
    protected static string $resource = ReactionBatchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
