<?php

namespace App\Filament\Resources\FacebookPostResource\RelationManagers;

use App\Filament\Exports\ReactionPostExporter;
use App\Filament\Resources\ReactionBatchResource;
use Carbon\Carbon;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Actions\Exports\ExportColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PostsRelationManager extends RelationManager
{
    protected static string $relationship = 'posts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        $total = $this->getOwnerRecord()['total'];
        if ($this->getOwnerRecord()->batch_id){
            $done = $this->getOwnerRecord()->posts()->where('status',1)->count();
        }else{
            $done = $this->getOwnerRecord()->posts()->count();
        }
        $label = "Đã hoàn thành $done / {$total}";
        return $table
            ->recordTitleAttribute('posts')
            ->heading($label)
            ->columns([
                Tables\Columns\TextColumn::make('url')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('post_id')
                    ->toggleable(),
                ...$this->getColumn()
            ])
            ->filters([
                //
            ])
            ->headerActions([
                ExportAction::make()
                    ->label("Export excel")
                    ->exporter(ReactionPostExporter::class)
                    ->formats([
                        ExportFormat::Xlsx,
                    ])
                    ->fileName($this->getOwnerRecord()->name)
                    ->chunkSize(250)
                    ->modifyQueryUsing(fn(Builder $query) => $query->orderBy('sort', 'asc'))

            ])
            ->actions([

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort', 'asc')
            ->poll(function () use ($total, $done) {
                if ($total == $done) {
                    return false;
                }
                return '1s';
            })
            ->paginated([50, 100, 200]);
    }

    private function getColumn()
    {
//        $owner = $this->getOwnerRecord();
//        if ($owner->batch_id && $owner->type == ReactionBatchResource::FACEBOOK){
//            return [
//                Tables\Columns\TextColumn::make('data.total')
//                    ->toggleable()
//                    ->label("Total"),
//                Tables\Columns\TextColumn::make('data.Like')
//                    ->toggleable()
//                    ->label("Like"),
//                Tables\Columns\TextColumn::make('data.Love')
//                    ->toggleable()
//                    ->label("Love"),
//                Tables\Columns\TextColumn::make('data.Haha')
//                    ->toggleable()
//                    ->label("Haha"),
//                Tables\Columns\TextColumn::make('data.Sad')
//                    ->toggleable()
//                    ->label("Sad"),
//                Tables\Columns\TextColumn::make('data.Care')
//                    ->toggleable()
//                    ->label("Care"),
//                Tables\Columns\TextColumn::make('data.Wow')
//                    ->toggleable()
//                    ->label("Wow"),
//                Tables\Columns\TextColumn::make('data.Angry')
//                    ->toggleable()
//                    ->label("Angry"),
//            ];
//        }
        return [
            Tables\Columns\TextColumn::make('data.count_like')
                ->toggleable()
                ->label("Like"),
            Tables\Columns\TextColumn::make('data.count_share')
                ->toggleable()
                ->label("Share"),
            Tables\Columns\TextColumn::make('data.count_comment')
                ->toggleable()
                ->label("Comment"),
            Tables\Columns\TextColumn::make('data.count_view')
                ->toggleable()
                ->label("View"),
        ];
    }
}
