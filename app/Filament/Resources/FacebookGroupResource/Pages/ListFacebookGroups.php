<?php

namespace App\Filament\Resources\FacebookGroupResource\Pages;

use App\Filament\Resources\FacebookGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFacebookGroups extends ListRecords
{
    protected static string $resource = FacebookGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
