<?php

namespace App\Filament\Resources\FacebookPageResource\Pages;

use App\Filament\Resources\FacebookPageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFacebookPages extends ListRecords
{
    protected static string $resource = FacebookPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
