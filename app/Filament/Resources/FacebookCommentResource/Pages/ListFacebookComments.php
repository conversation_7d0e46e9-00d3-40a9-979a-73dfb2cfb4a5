<?php

namespace App\Filament\Resources\FacebookCommentResource\Pages;

use App\Filament\Resources\FacebookCommentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFacebookComments extends ListRecords
{
    protected static string $resource = FacebookCommentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
