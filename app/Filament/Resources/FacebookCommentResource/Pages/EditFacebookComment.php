<?php

namespace App\Filament\Resources\FacebookCommentResource\Pages;

use App\Filament\Resources\FacebookCommentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFacebookComment extends EditRecord
{
    protected static string $resource = FacebookCommentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
