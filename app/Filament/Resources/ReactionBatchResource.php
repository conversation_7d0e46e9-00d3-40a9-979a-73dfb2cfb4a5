<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FacebookPostResource\RelationManagers\PostsRelationManager;
use App\Filament\Resources\ReactionBatchResource\Pages;
use App\Filament\Resources\ReactionBatchResource\RelationManagers;
use App\Models\ReactionBatch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReactionBatchResource extends Resource
{
    protected static ?string $model = ReactionBatch::class;
    protected static ?string $label = "Đánh giá ";
    protected static ?string $navigationGroup = 'Reaction';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    const TIKTOK = 1;
    const YOUTUBE = 2;
    const INSTAGRAM = 3;
    const THREADS = 4;
    const FACEBOOK = 5;
    const FACEBOOK_2 = 6;
    const TYPE = [
        self::TIKTOK => 'Tiktok',
        self::YOUTUBE => 'Youtube',
        self::INSTAGRAM => 'Instagram',
        self::THREADS => 'Threads',
        self::FACEBOOK => 'Facebook',
        self::FACEBOOK_2 => 'Facebook V2',
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->required(),
                Forms\Components\Select::make('type')
                    ->options(self::TYPE)->required(),
                Forms\Components\Textarea::make('links')
                    ->rows(5)
                    ->required()
                    ->helperText("Mỗi link 1 dòng")
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('type')->formatStateUsing(function ($record) {
                    return self::TYPE[$record->type];
                }),
            ])
            ->filters([
                //
            ])
            ->actions([
//                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            PostsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReactionBatches::route('/'),
            'create' => Pages\CreateReactionBatch::route('/create'),
//            'edit' => Pages\EditReactionBatch::route('/{record}/edit'),
            'view' => Pages\ViewReactionBatch::route('/{record}'),

        ];
    }
//    public static function canEdit(Model $record): bool
//    {
//        return false; // TODO: Change the autogenerated stub
//    }
}
