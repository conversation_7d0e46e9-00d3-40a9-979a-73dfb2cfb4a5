<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FacebookPostResource\Pages;
use App\Filament\Resources\FacebookPostResource\RelationManagers;
use App\Models\FacebookPost;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FacebookPostResource extends Resource
{
    protected static ?string $model = FacebookPost::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    const TYPE_GET = [
        0 => 'Default',
        1 => 'Full'
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('post_id'),
                Forms\Components\Select::make('type')
                    ->options([
                        0 => 'Group',
                        1 => 'Page'
                    ])
                    ->default(0),
                Forms\Components\TextInput::make('type_get')
                    ->label("Max page")
                    ->default(8),
                Forms\Components\Textarea::make('cursor'),
                Toggle::make('status'),
                Toggle::make('is_first'),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('post_id')
                    ->copyable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn($state) => match ($state) {
                        0 => 'warning',
                        1 => 'success',
                    })
                    ->formatStateUsing(fn($state) => match ($state) {
                        0 => 'Post',
                        1 => 'Page',
                    })
                ,
                Tables\Columns\ToggleColumn::make('status'),
//                Tables\Columns\ToggleColumn::make('is_first'),
                Tables\Columns\TextColumn::make('created_at'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacebookPosts::route('/'),
            'create' => Pages\CreateFacebookPost::route('/create'),
            'edit' => Pages\EditFacebookPost::route('/{record}/edit'),
        ];
    }
}
