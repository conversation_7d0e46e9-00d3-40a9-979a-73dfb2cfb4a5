<?php

namespace App\Filament\Resources\FacebookPostResource\RelationManagers;

use App\Filament\Exports\CommentExporter;
use App\Filament\Exports\ReactionPostExporter;
use Carbon\Carbon;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CommentsRelationManager extends RelationManager
{
    protected static string $relationship = 'comments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('comment_id'),
                Forms\Components\TextInput::make('data.username'),
                Forms\Components\TextInput::make('data.comment'),
                Forms\Components\TextInput::make('data.user_id'),
                Forms\Components\TextInput::make('created_time')
                    ->state(function ( $record) {
                        return Carbon::parse($record->created_time)->timezone('Asia/Ho_Chi_Minh');
                    })
                ,
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('comments')
            ->columns([
                Tables\Columns\TextColumn::make('comment_id'),
                Tables\Columns\TextColumn::make('data.username'),
                Tables\Columns\TextColumn::make('created_time')
                    ->formatStateUsing(function ($record) {
                        return Carbon::parse($record->created_time)->timezone('Asia/Ho_Chi_Minh');
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
                ExportAction::make()
                    ->label("Export excel")
                    ->exporter(CommentExporter::class)
                    ->formats([
                        ExportFormat::Xlsx,
                    ])
                    ->chunkSize(250)
                    ->modifyQueryUsing(fn (Builder $query) => $query->orderBy('created_time','desc'))

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_time','desc');
    }
}
