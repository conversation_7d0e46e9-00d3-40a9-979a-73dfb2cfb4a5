<?php

namespace App\Filament\Resources;

use App\Filament\Action\PushJobAction;
use App\Filament\Resources\FacebookGroupResource\Pages;
use App\Filament\Resources\FacebookGroupResource\RelationManagers;
use App\Models\FacebookGroup;
use App\Models\UserPost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Artisan;

class FacebookGroupResource extends Resource
{
    protected static ?string $model = FacebookGroup::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('page_id'),
                Forms\Components\TextInput::make('page_name'),
                Forms\Components\TextInput::make('page_link'),
                Forms\Components\Toggle::make('is_priority'),
                Forms\Components\Toggle::make('is_first'),
                Forms\Components\TextInput::make('cursor_fb'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('page_id')
                    ->searchable()
                    ->copyable()
                ,
                Tables\Columns\TextColumn::make('page_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('cursor_fb')
                    ->wrap()
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                PushJobAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->headerActions([
                Action::make('run_all')
                    ->label('Run All')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Xác nhận chạy tất cả')
                    ->modalDescription('Bạn có chắc chắn muốn chạy tất cả không? Hành động này không thể hoàn tác.')
                    ->modalButton('Chạy ngay') // Nút xác nhận
                    ->action(fn() => self::runAllTasks()),
                Action::make('clear_all')
                    ->label('Clear all job')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Xác nhận chạy tất cả')
                    ->modalDescription('Bạn có chắc chắn muốn chạy tất cả không? Hành động này không thể hoàn tác.')
                    ->modalButton('Clear') // Nút xác nhận
                    ->action(fn() => self::clearJob()),
            ])
            ->defaultSort('created', 'desc');
    }

    public static function clearJob()
    {
        \Illuminate\Support\Facades\Redis::del('jobs_queue_workers');
        Notification::make()
            ->title("Success")
            ->body("Run task success")
            ->success()
            ->send();
    }

    public static function runAllTasks()
    {
        dispatch(function () {
            Artisan::call('app:sendjob');
        });
//        $totalWoker = getConfig('total_worker');
//        $groups = FacebookGroup::with(['latestPost' => function ($query) {
//            $query->orderByDesc(\DB::raw("JSON_UNQUOTE(JSON_EXTRACT(message, '$._values.fb_created'))"));
//        }])->where('type',1)->limit(10000)->get();
////        foreach ($groups as $k => $group) {
////            $groups[$k]["latest_post_id"] = $group->latestPost
////                ? explode('_', $group->latestPost->fb_post_id)[1]
////                : null;
////        }
//
//
//        $groupChunks = array_chunk($groups->toArray(), $totalWoker);
//
//        foreach ($groupChunks as $chunk) {
////            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_workers_test', json_encode($chunk));
//            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_workers', json_encode($chunk));
//        }


        Notification::make()
            ->title("Success")
            ->body("Run task success")
            ->success()
            ->send();
    }

    protected function headerActions(): array
    {
        return [

        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacebookGroups::route('/'),
            'create' => Pages\CreateFacebookGroup::route('/create'),
            'edit' => Pages\EditFacebookGroup::route('/{record}/edit'),
        ];
    }


}
