<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProxyResource\Pages;
use App\Models\Proxy;
use Dotswan\FilamentCodeEditor\Fields\CodeEditor;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Table;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

class ProxyResource extends Resource
{
    protected static ?string $model = Proxy::class;
    protected static ?string $navigationGroup = 'Proxies';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name'),
                Forms\Components\TextInput::make('api_key'),
                Select::make('provider')
                    ->options([
                        1 => 'WWProxy',
                        3 => 'TmpProxy',
                    ]),
                Forms\Components\TextInput::make('extra.url_api'),
                Forms\Components\TextInput::make('extra.url_rotating'),
                Forms\Components\TextInput::make('extra.worker')
                ,Select::make('extra.type')
                    ->options([
                        'fb_comment' => 'FB Comment',
                        'default' => 'Default',
                        'page' => 'Page',
                        'test' => 'Test',
                    ]),
                JsonColumn::make('extra.field_map')
                    ->columnSpan(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name'),
                Tables\Columns\TextColumn::make('api_key'),
                Tables\Columns\TextColumn::make('provider'),
                Tables\Columns\ToggleColumn::make('active'),
                Tables\Columns\TextColumn::make('extra.type')->label("Type"),
//                Tables\Columns\TextColumn::make('time_fail'),
//                Tables\Columns\TextColumn::make('time_success'),
                Tables\Columns\TextColumn::make('updated_at'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                DeleteAction::make()
            ])
            ->headerActions([
                Action::make('run_all')
                    ->label('Sync To Redis')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalButton('Sync') // Nút xác nhận
                    ->action(fn() => self::sync()), // Gọi hàm khi xác nhận
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_at', 'desc');
    }

    public static function sync()
    {
        $proxies = Proxy::where('provider',1)->get()->toArray();
        \Illuminate\Support\Facades\Redis::client()->del('proxies');
        \Illuminate\Support\Facades\Redis::client()->sadd('proxies', json_encode($proxies));
        Notification::make()
            ->title("Success")
            ->body("Sync success")
            ->success()
            ->send();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProxies::route('/'),
            'create' => Pages\CreateProxy::route('/create'),
            'edit' => Pages\EditProxy::route('/{record}/edit'),
        ];
    }
}
