<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MonitazViaResource\Pages;
use App\Filament\Resources\MonitazViaResource\RelationManagers;
use App\Models\MonitazVia;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MonitazViaResource extends Resource
{
    protected static ?string $model = MonitazVia::class;
    protected static ?string $label = "Via Token ";
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Facebook Token';
    const TYPE_BM = [
        0 => 'Nhóm Private',
        1 => 'Nhóm Bình thường',
        2 => 'Nhóm Backup Bình thường',
        3 => 'Nhóm Backup Private',
        4 => 'Nhóm Private 2',
        5 => 'Nhóm Backup Private 2',
    ];
    const STATUS_VIA = [
        0 => 'Die',
        1 => 'Live'
    ];
    const STATUS_TOKEN = [
        0 => 'Die',
        1 => 'Live'
    ];

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\Textarea::make('cookie')
//                    ->label("Data")
//                    ->helperText("NAME BM|UID|PASSWORD|EMAIL|2FA|COOKIE"),
                Forms\Components\TextInput::make('uid')
                    ->autocomplete(false)->required(),
                Forms\Components\TextInput::make('name_bm')
                    ->autocomplete(false)->required(),
                Forms\Components\TextInput::make('email')
                    ->autocomplete(false)->required(),
                Forms\Components\TextInput::make('pass')
                    ->autocomplete(false)->required(),
                Forms\Components\TextInput::make('2fa')
                    ->autocomplete(false),
                Forms\Components\Textarea::make('cookie')
                    ->autocomplete(false)->columnSpan(2),
                Forms\Components\Textarea::make('token')
                    ->autocomplete(false)->columnSpan(2),
                Forms\Components\Select::make('type_bm')
                    ->options(self::TYPE_BM)
                    ->default(1)
                    ->native(false),
                Forms\Components\Select::make('status_via')
                    ->options(self::STATUS_VIA)
                    ->default(1)
                    ->native(false),
                Forms\Components\Select::make('status_token')
                    ->options(self::STATUS_TOKEN)
                    ->default(1)
                    ->native(false),
                Toggle::make('is_pending')
                    ->default(1)
                    ->columnSpanFull()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('uid')
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_bm')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type_bm')
                    ->formatStateUsing(fn($state) => self::TYPE_BM[$state] ?? 'Không xác định'),
                Tables\Columns\TextColumn::make('status_via')
                    ->badge()
                    ->sortable()
                    ->formatStateUsing(fn($record) => ['Die', 'Live'][$record->status_via])
                    ->color(fn($state) => match ($state) {
                        0 => 'danger',
                        1 => 'success',
                    }),
                Tables\Columns\TextColumn::make('status_token')
                    ->badge()
                    ->sortable()
                    ->formatStateUsing(fn($record) => ['Die', 'Live'][$record->status_token])
                    ->color(fn($state) => match ($state) {
                        0 => 'danger',
                        1 => 'success',
                    })
                ,
                IconColumn::make('is_pending')->boolean(),
                Tables\Columns\TextColumn::make('created_at'),
                Tables\Columns\TextColumn::make('updated_at'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMonitazVias::route('/'),
            'create' => Pages\CreateMonitazVia::route('/create'),
            'edit' => Pages\EditMonitazVia::route('/{record}/edit'),
        ];
    }
}
