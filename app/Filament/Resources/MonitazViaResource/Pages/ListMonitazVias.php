<?php

namespace App\Filament\Resources\MonitazViaResource\Pages;

use App\Filament\Imports\MonitazViaImporter;
use App\Filament\Resources\MonitazViaResource;
use App\Jobs\Redis\ImportViaCsv;
use EightyNine\ExcelImport\ExcelImportAction;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms\Components\Actions\Action;

class ListMonitazVias extends ListRecords
{
    protected static string $resource = MonitazViaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(MonitazViaImporter::class)
                ->job(ImportViaCsv::class),
            Actions\CreateAction::make(),
        ];
    }
}
