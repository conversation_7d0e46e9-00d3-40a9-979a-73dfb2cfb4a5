<?php

namespace App\Filament\Resources\MonitazViaResource\Pages;

use App\Filament\Resources\MonitazViaResource;
use App\Service\FacebookToken;
use App\Service\FacebookToken2;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateMonitazVia extends CreateRecord
{
    protected static string $resource = MonitazViaResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $cookie = $data['cookie'];
        if (!empty($cookie)){
            unset($data['cookie']);
//            $token = new FacebookToken($cookie,'124024574287414');
            $token = new FacebookToken2($cookie);
            $token = $token->getToken();
            $token = @$token['token2'] ?? null;
            if (empty($token)) {
                Notification::make()
                    ->title("No token")
                    ->danger()
                    ->send();
                $this->halt();
            }
            $data['token'] = $token;
        }

        return $data;
    }
}
