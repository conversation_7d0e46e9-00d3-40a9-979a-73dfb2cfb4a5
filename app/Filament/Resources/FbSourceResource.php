<?php

namespace App\Filament\Resources;

use App\Enum\FbSourceEnum;
use App\Filament\Resources\FbSourceResource\Pages;
use App\Filament\Resources\FbSourceResource\RelationManagers;
use App\Models\FbSource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FbSourceResource extends Resource
{
    protected static ?string $model = FbSource::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('source_id')->copyable()->searchable(),
                Tables\Columns\TextColumn::make('extra.page_name')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('source_type')
                    ->sortable()
                    ->label('Source Type')
                    ->formatStateUsing(function ($record){
                        return FbSourceEnum::tryFrom(is_null($record->source_type) ? 0 : $record->source_type )?->name ?? 'Unknown';
                    }),
                Tables\Columns\TextColumn::make('uid')->sortable(),
                IconColumn::make('is_sync')
                    ->sortable()
                    ->boolean()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_at','desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFbSources::route('/'),
            'create' => Pages\CreateFbSource::route('/create'),
            'edit' => Pages\EditFbSource::route('/{record}/edit'),
        ];
    }
}
