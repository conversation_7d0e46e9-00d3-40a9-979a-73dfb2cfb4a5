<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FacebookCommentResource\Pages;
use App\Filament\Resources\FacebookCommentResource\RelationManagers;
use App\Models\FacebookComment;
use App\Models\FbComments;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FacebookCommentResource extends Resource
{
    protected static ?string $model = FbComments::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('comment_id'),
                Forms\Components\TextInput::make('data.username'),
                Forms\Components\TextInput::make('data.comment'),
                Forms\Components\TextInput::make('data.user_id'),
                Forms\Components\TextInput::make('user_id'),
                Forms\Components\TextInput::make('created_time'),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('comment_id'),
                Tables\Columns\TextColumn::make('data.username'),
                Tables\Columns\TextColumn::make('user_id')->searchable()
                ->sortable(),
                Tables\Columns\TextColumn::make('created_time')
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        return Carbon::parse($record->created_time)->timezone('Asia/Ho_Chi_Minh');
                    }),
                Tables\Columns\TextColumn::make('created_at')
                ->sortable()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created_time', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacebookComments::route('/'),
//            'create' => Pages\CreateFacebookComment::route('/create'),
            'edit' => Pages\EditFacebookComment::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
