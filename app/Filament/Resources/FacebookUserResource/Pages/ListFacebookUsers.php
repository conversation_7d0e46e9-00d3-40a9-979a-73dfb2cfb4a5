<?php

namespace App\Filament\Resources\FacebookUserResource\Pages;

use App\Filament\Resources\FacebookUserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFacebookUsers extends ListRecords
{
    protected static string $resource = FacebookUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
