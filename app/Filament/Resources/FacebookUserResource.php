<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FacebookUserResource\Pages;
use App\Filament\Resources\FacebookUserResource\RelationManagers;
use App\Models\FacebookUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FacebookUserResource extends Resource
{
    protected static ?string $model = FacebookUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Facebook User Page';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('page_id'),
                Forms\Components\TextInput::make('page_link'),
                Forms\Components\TextInput::make('page_name'),
                Forms\Components\Toggle::make('is_have_data')
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('page_id'),
                Tables\Columns\TextColumn::make('page_link'),
                Tables\Columns\TextColumn::make('page_name'),
                Tables\Columns\TextColumn::make('is_have_data'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('created','desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacebookUsers::route('/'),
            'create' => Pages\CreateFacebookUser::route('/create'),
            'edit' => Pages\EditFacebookUser::route('/{record}/edit'),
        ];
    }
}
