<?php

namespace App\Filament\Action;

use App\Jobs\ProcessTest;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class PushJobAction  extends Action
{
    public static function make(string|null $name = 'approve'): static
    {
        return parent::make($name)
            ->icon('heroicon-m-rocket-launch')
            ->color('success')
            ->label('Push Job')
            ->size('sm')
            ->action(function (array $data, $record): void {
                $record["latest_post_id"] = $record->latestPost
                    ? explode('_', $record->latestPost->fb_post_id)[1]
                    : null;
                \Illuminate\Support\Facades\Redis::rpush('jobs_queue_workers', json_encode([$record]));
                Notification::make()
                    ->title('Send  thành công')
                    ->success()
                    ->send();
            })
            ->outlined(false)
            ->button();
    }
}
