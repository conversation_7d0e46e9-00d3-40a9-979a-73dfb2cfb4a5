<?php

namespace App\Filament\Action;

use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Log;

class PushJobPageAction extends Action
{
    public static function make(string|null $name = 'approve'): static
    {
        return parent::make($name)
            ->icon('heroicon-m-rocket-launch')
            ->color('success')
            ->label('Push Job')
            ->size('sm')
            ->action(function (array $data, $record): void {
                \Illuminate\Support\Facades\Redis::rpush('jobs_queue_fb_pages', json_encode([$record]));

                Notification::make()
                    ->title('Send  thành công')
                    ->success()
                    ->send();
            })
            ->outlined(false)
            ->button();
    }
}
