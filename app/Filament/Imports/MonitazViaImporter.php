<?php

namespace App\Filament\Imports;

use App\Models\MonitazVia;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class MonitazViaImporter extends Importer
{
    protected static ?string $model = MonitazVia::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('uid'),
            ImportColumn::make('name_bm'),
            ImportColumn::make('email'),
            ImportColumn::make('pass'),
            ImportColumn::make('2fa'),
            ImportColumn::make('cookie'),
            ImportColumn::make('type_bm'),
            ImportColumn::make('status_via'),
            ImportColumn::make('status_token'),
        ];
    }

    public function resolveRecord(): ?MonitazVia
    {
        return MonitazVia::firstOrNew([
            'uid' => $this->data['uid'],
        ]);
    }
    protected function beforeFill(): void
    {

    }
    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your monitaz via import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';
        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }
        return $body;
    }

    public function getJobConnection(): ?string
    {
        return 'redis';
    }

}
