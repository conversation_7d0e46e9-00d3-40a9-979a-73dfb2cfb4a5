<?php

namespace App\Console\Commands;

use App\Models\Proxy;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SetProxy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-proxy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set proxy lên redis';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (empty(get_config('cron_roating'))){
            return false;
        }
        $wwProxies = Proxy::query()->where('provider', 1)->get();
        $responses = Http::pool(
            fn($pool) => $wwProxies->map(fn(Proxy $proxy) => $pool->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => "application/json"
            ])->get($proxy["extra"]["url_api"])
            )
        );
        $this->insertProxies($this->mapProxy($responses,$wwProxies));

        $tmpProxies = Proxy::query()->where('provider', 3)->get();
        $responses = Http::pool(
            fn($pool) => $tmpProxies->map(fn(Proxy $proxy) => $pool->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => "application/json"
            ])->post($proxy["extra"]["url_api"],[
                "api_key" => $proxy->api_key
            ])
            )
        );
        $this->insertProxies($this->mapProxy($responses,$tmpProxies));
        $this->output->success("done");
    }
    function insertProxies($items = [])
    {
        foreach ($items as $key => $value) {
            \Illuminate\Support\Facades\Redis::client()->hset('proxies_items', $key, json_encode($value));
        }
    }
    function mapProxy($responses,$proxies)
    {
        $items = [];
        foreach ($responses as $response) {
            $url = @$response->handlerStats()["url"] ?? '';
            $proxy = $proxies->where("extra.url_api", $url)->first();
            if ($response->successful() && $proxy) {
                $json = $response->json();
                if (empty($json)) {
                    continue;
                }
                $map = $proxy['extra']['field_map'] ?? [];
                if ($json[$map["status_field"]] != $map["status_value"]) {
                    continue;
                }
                $value = $json[$map["data_field"]];
                $mapField = $map["template"];

                $item = array_map(function ($path) use ($value, $map) {
                    $val = self::getValue($value, $path);
                    return $val;
                }, $mapField);
                $px = "";
                if (isset($map['auth']) && $map['auth'] == true) {
                    $px = "{$item['username']}:{$item['password']}@";
                    if (!str_contains($item["ip"], ":")) {
                        $px .= "{$item["ip"]}:{$item["port"]}";
                    }else{
                        $px .= "{$item["ip"]}";
                    }

                } else {
                    $px = "{$item["ip"]}:{$item["port"]}";
                }
                $items[$proxy['id']] = [
                    'proxy' => $px,
                    'use_time' => 0,
                    'worker' => @$proxy['extra']['worker'],
                    'type' => @$proxy['extra']['type'] ?? 'default'
                ];
            }
        }

        return $items;

    }
    function getValue($data, $path)
    {
        $keys = explode('.', $path);
        foreach ($keys as $key) {
            if (is_array($data) && array_key_exists($key, $data)) {
                $data = $data[$key];
            } else {
                return null;
            }
        }
        return $data;
    }
}

