<?php

namespace App\Console\Commands;

use App\Models\FacebookGroup;
use App\Models\Proxy;
use App\Service\TelegramService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class SendJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sendjob {type?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (empty(get_config('enable'))){
            $this->output->info("No enable");
            return;
        }
        $type = $this->argument('type');

        $totalWorker = get_config('total_worker');
        if (empty($type)){
//            TelegramService::sendMessage("Send job group");
            FacebookGroup::query()->where('is_priority',1)->chunkById($totalWorker, function ($groups) {
                \Illuminate\Support\Facades\Redis::rpush('jobs_queue_workers', json_encode($groups));
            });
            FacebookGroup::query()->where('is_priority',0)->chunkById($totalWorker, function ($groups) {
                \Illuminate\Support\Facades\Redis::rpush('jobs_queue_workers', json_encode($groups));
            });
            $this->output->success("done, total " . FacebookGroup::count());
            return;
        }

//        TelegramService::sendMessage("Send job next cursor group");
        FacebookGroup::query()->where('is_priority',1)
            ->whereNotNull('next_cursor')
            ->chunkById($totalWorker, function ($groups) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_group_next_cursor_workers', json_encode($groups));
        });
        FacebookGroup::query()->where('is_priority',0)
            ->whereNotNull('next_cursor')
            ->chunkById($totalWorker, function ($groups) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_group_next_cursor_workers', json_encode($groups));
        });
        $this->output->success("done");
        return;
    }


}


