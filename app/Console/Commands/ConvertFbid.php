<?php

namespace App\Console\Commands;

use App\Enum\FbSourceEnum;
use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use App\Models\FacebookUser;
use App\Models\FbComments;
use App\Models\FbSource;
use App\Service\TelegramService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;

class ConvertFbid extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-pfbid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->check();
    }


    private function check()
    {
        $sources = FbComments::query()
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(data, '$.user_id')) LIKE 'pfbid%'")
            ->whereNull('user_id')
            ->limit(50)->get();
        $proxies = collect(Redis::hgetall('proxies_items') ?? []);
        $proxies = $proxies->map(fn($item) => json_decode($item,true));
        $proxyPage  = $proxies->where('type','page')->random();
        if (empty($proxyPage)){
            $this->output->info("No proxy");
            return;
        }
        $responses = Http::pool(function ($pool) use ($sources,$proxyPage) {
            foreach ($sources as $source) {
                $pfbid = $source->data['user_id'];
                $pool->withHeaders($this->headers())
                    ->withOptions([
                        'proxy' => "http://{$proxyPage['proxy']}",
                    ])
                    ->get("https://www.facebook.com/{$pfbid}?ik={$source->id}");
            }
        });
        $updates = [];
        $totalDone = 0;
        foreach ($responses as $index => $response) {
            try {
                if ($response instanceof \Illuminate\Http\Client\ConnectionException){
                    continue;
                }
                $body = $response->body();
                $url = @$response->handlerStats()["url"] ?? '';
                $parsedUrl = parse_url($url);
                parse_str($parsedUrl['query'] ?? '', $queryParams);
                $id = $queryParams['ik'] ?? null;
                if (empty($id)) {
                    continue;
                }
                if ($this->isDie($body)){
                    continue;
                }
                $userId = $this->getPage($body);
                if (empty($userId)){
                    continue;
                }
                $comment = $sources->where('id',$id)->first();
                $comment->user_id = $userId;
                $comment->save();
                $totalDone += 1;
            }catch (\Exception $exception){}
        }

        $this->output->success("done {$totalDone}");
    }
    private function isDie($content)
    {
        return str_contains($content, "isn't available");
    }


    private function getPage($content)
    {
        $regex = '/"userID"\s*:\s*"(\d+)"/';
        if (!preg_match($regex, $content, $matches)) {
            return null;
        }

        return $matches[1];
    }
    private function headers()
    {
        return [
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8',
            'cache-control' => 'no-cache',
            'dpr' => '0.90625',
            'pragma' => 'no-cache',
            'priority' => 'u=0, i',
            'sec-ch-prefers-color-scheme' => 'dark',
            'sec-ch-ua' => '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-full-version-list' => '"Chromium";v="136.0.7103.113", "Google Chrome";v="136.0.7103.113", "Not.A/Brand";v="99.0.0.0"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-model' => '""',
            'sec-ch-ua-platform' => '"Linux"',
            'sec-ch-ua-platform-version' => '"6.11.0"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'same-origin',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => '1',
            'user-agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'viewport-width' => '1508',
        ];
    }
}

