<?php

namespace App\Console\Commands;


use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use App\Models\FacebookPost;
use App\Service\TelegramService;
use Illuminate\Console\Command;

class SendPostCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sendjob-post';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Job get comment post';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $totalWorker = get_config('total_worker');

        FacebookPost::query()
            ->where('status',1)
            ->orderBy('curr_page','desc')
            ->orderBy('time_check', 'asc')
            ->chunkById($totalWorker, function ($groups) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_fb_posts', json_encode($groups));
        });

        $count = FacebookPost::query()
            ->where('status',1)->count();

//        TelegramService::sendMessage("Send job comment: tổng {$count} posts");
        $this->output->success("done, total " . $count);
    }


}

