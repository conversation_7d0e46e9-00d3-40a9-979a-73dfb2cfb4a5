<?php

namespace App\Console\Commands;

use App\Models\Counter;
use App\Models\FbSource;
use App\Models\MonitazVia;
use App\Models\PostFacebookPage;
use App\Models\Proxy;
use App\Models\UserPost;
use App\Service\TelegramService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CountGroupPost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->facebookSource();
        $this->viaStatus();

        $from = Carbon::now()->subMinutes(15);
        $to = Carbon::now();
        $count = Counter::query()
            ->where('type', 0)
            ->whereBetween('created_at', [$from, $to])
            ->sum('count');
        TelegramService::sendMessage("{$count} bài viết trong vòng 15 phút");
        $countUserPage = Counter::query()
            ->where('type', 1)
            ->whereBetween('created_at', [$from, $to])
            ->sum('count');
        $content = "<b>Facebook crawler Report (last 15 min)</b>\n";
        $content .= "<code>Total group: {$count}</code>\n";
        $content .= "<code>Total user page: {$countUserPage}</code>\n";
        TelegramService::sendMessage($content);

        $this->output->success("done");
    }


    private function facebookSource()
    {
        $now = Carbon::now();
        $from = $now->copy()->subMinutes(15);
        $to = $now;

        // Tạo nội dung báo cáo
        $content = "<b>Facebook Source Report (last 15 min)</b>\n";

        // Số lượng source chưa check
        $countNoCheck = FbSource::whereNull('source_type')->count();
        $content .= "<code>Chưa check:</code> <b>{$countNoCheck}</b>\n";
        $counters = Counter::query()
            ->whereIn('type', [2, 4])
            ->whereBetween('created_at', [$from, $to])
            ->get()
            ->groupBy('type')
            ->map(fn($items) => $items->sum('count'));

        $checked = $counters[4] ?? 0;
        $synced = $counters[2] ?? 0;
        $content .= "<code>Đã check:</code> <b>{$checked}</b>\n";
        $content .= "<code>Đã sync:</code> <b>{$synced}</b>\n";
        TelegramService::sendMessage($content);
    }


    private function viaStatus()
    {
        $from = Carbon::now()->subMinutes(15);
        $to = Carbon::now();

        $totalPending = MonitazVia::query()
            ->where('is_pending',1)
            ->where('type_bm',1)
            ->count();

        $content = "<b>Facebook Via Report (last 15 min)</b>\n";
        $content .= "<code>Total via Pending: {$totalPending}</code>\n";

        $ttokenLive = MonitazVia::query()
            ->where('is_pending',0)
            ->where('status_token',1)
            ->where('type_bm',1)
            ->get();
        $content .= "<code>Token live:</code>\n";

        foreach ($ttokenLive as $item) {
            $content .= "<code>{$item->uid} - {$item->name_bm}</code>\n";
        }
        TelegramService::sendMessage($content);

    }
}

