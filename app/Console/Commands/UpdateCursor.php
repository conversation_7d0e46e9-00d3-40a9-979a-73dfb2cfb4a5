<?php

namespace App\Console\Commands;

use App\Models\Counter;
use App\Models\FacebookGroup;
use App\Service\TelegramService;
use Carbon\Carbon;
use DB;
use Illuminate\Console\Command;

class UpdateCursor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-cursor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        FacebookGroup::whereNotNull('cursor_fb')
            ->update([
                'next_cursor' => DB::raw('cursor_fb'),
                'cursor_fb' => null,
                'cursor_count' => 0
            ]);

//        TelegramService::sendMessage("update next cursor group done");
        $this->output->success("done");
    }
}

