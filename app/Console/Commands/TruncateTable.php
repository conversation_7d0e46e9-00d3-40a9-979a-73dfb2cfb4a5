<?php

namespace App\Console\Commands;

use App\Models\LastIdCheck;
use App\Models\PostFacebookPage;
use App\Models\UserPost;
use App\Service\TelegramService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class TruncateTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:truncate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate 2 bảng users_posts_raw,users_posts_raw_page và reset id';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (empty(get_config('auto_truncate'))){
            $this->output->info("Auto truncate not enable");
            return false;
        }
        try {
            PostFacebookPage::truncate();
            UserPost::truncate();
            LastIdCheck::query()
                ->whereIn('name_table', ['users_posts_raw', 'users_posts_raw_page'])
                ->update([
                    'last_id' => 1
                ]);

            $date = Carbon::now()->toDateString();
            TelegramService::sendMessage("{$date} Auto truncate done!");
            $this->output->success("Truncate success !!");

        } catch (\Exception $exception) {
            $this->output->error($exception->getMessage());
        }
    }
}
