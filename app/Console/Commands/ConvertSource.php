<?php

namespace App\Console\Commands;

use App\Enum\FbSourceEnum;
use App\Models\Counter;
use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use App\Models\FacebookUser;
use App\Models\FbSource;
use App\Service\TelegramService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Process\Pool;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
ini_set('max_execution_time', 500);
ini_set('memory_limit', '2048M');

class ConvertSource extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-source {type?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        if ($type == 'check') {
            $this->check();
            return;
        }
        if ($type == 'sync') {
            $this->sync();
            return;
        }

    }

    private function sync()
    {
        $types = [
            FbSourceEnum::GROUP->value => [
                'limit' => 50,
                'model' => FacebookGroup::class,
                'page_type' => 'group',
                'group_page' => 'GR106',
                'extra' => [
                    'number_posts_last_day' => 0,
                    'scanning_frequency' => 5,
                    'status_get_posts_no_auth' => 0,
                    'status_get_info' => 0,
                    'count_post' => 0,
                    'type' => 1,
                    'type_scan' => 0,


                ]
            ],
            FbSourceEnum::PAGE->value => [
                'limit' => 50,
                'model' => FacebookPage::class,
                'page_type' => 'page',
                'extra' => [
                    'status_get_posts_no_auth' => 0,
                    'count_post' => 0,
                    'type' => 1,
                    'type_scan' => 0,

                ]
            ],
            FbSourceEnum::USER->value => [
                'limit' => 50,
                'model' => FacebookUser::class,
                'page_type' => 'User',
                'group_page' => 'GR106',
                'extra' => []

            ],
        ];

        $results = [];

        $totalSync = 0;
        foreach ($types as $type => $config) {
            $items = FbSource::query()
                ->where('source_type', $type)
                ->where('is_sync', false)
                ->limit($config['limit'])
                ->get();

            if ($items->isEmpty()) {
                $results[$type] = 0;
                continue;
            }

            $dataInsert = $this->buildInsertData($items, $config['page_type'], $config['extra']);

            $sourceIds = Arr::pluck($dataInsert, 'page_id');

            $existing = $config['model']::whereIn('page_id', $sourceIds)
                ->pluck('page_id')
                ->toArray();

            // Lọc ra những dòng chưa tồn tại
            $newData = array_filter($dataInsert, fn($row) => !in_array($row['page_id'], $existing));
            if (!empty($newData)) {
                $config['model']::insert($newData);
                $totalSync += count($newData);
            }
//            $config['model']::insertOrIgnore($dataInsert);
            FbSource::whereIn('id', $items->pluck('id'))->update(['is_sync' => true]);
        }
        Counter::create([
            'count' => $totalSync,
            'type' => 3,
        ]);
        $this->output->success(sprintf(
            "Sync thành công Group",
        ));
    }

    private function buildInsertData($items, $pageType, $extra)
    {
        return $items->map(function ($item) use ($pageType, $extra) {
            return [
                'page_id' => $item->uid,
                'page_type' => $pageType,
                'is_priority' => 1,
                'created' => time(),
                'status' => 1,
                'branch' => 'MEDIA',
                'is_code' => 200,
                'page_name' => $item->extra['page_name'] ?? '',
                'page_link' => "https://www.facebook.com/{$item->source_id}",
                'page_like_count' => 0,
                'lastest_post' => 0,
                'group_page' => 'GR1111',
                'sub_branch' => 'News',
                ...$extra
            ];
        })->toArray();
    }

    private function check()
    {
        $sources = FbSource::query()->whereNull('source_type')->limit(50)->get();

        $proxies = collect(Redis::hgetall('proxies_items') ?? []);
        $proxies = $proxies->map(fn($item) => json_decode($item, true));
        $proxyPage = $proxies->where('type', 'page')->random();
        if (empty($proxyPage)) {
            $this->output->info("No proxy");
            return;
        }
        $responses = Http::pool(function ($pool) use ($sources, $proxyPage) {
            foreach ($sources as $source) {
                $pool->withHeaders($this->headers())
                    ->withOptions([
                        'proxy' => "http://{$proxyPage['proxy']}",
                    ])
                    ->get("https://www.facebook.com/{$source->source_id}?ik={$source->source_id}");
            }
        });
        $updates = [];
        foreach ($responses as $index => $response) {
            try {
                if ($response instanceof \Illuminate\Http\Client\ConnectionException) {
                    continue;
                }
                $url = @$response->handlerStats()["url"] ?? '';
                $parsedUrl = parse_url($url);
                parse_str($parsedUrl['query'] ?? '', $queryParams);
                $id = $queryParams['ik'] ?? null;
                if (empty($id)) {
                    continue;
                }
                $body = $response->body();
                $type = 4;
                $uid = null;

                if ($this->isDie($body)) {
                    $type = FbSourceEnum::DIE->value;
                } elseif ($groupId = $this->getGroup($body)) {
                    $type = FbSourceEnum::GROUP->value;
                    $uid = $groupId;
                } elseif ($pageId = $this->getPage($body)) {
                    $uid = $pageId;
                    $type = $uid == $id ? FbSourceEnum::USER->value : FbSourceEnum::PAGE->value;
                }

                $updates[] = [
                    'source_id' => $id,
                    'uid' => $uid,
                    'source_type' => $type,
                ];
            } catch (\Exception $exception) {
            }

        }
        if (empty($updates)) {
            $this->output->info("No data");
            return;
        }
        Counter::create([
            'count' => count($updates),
            'type' => 4,
        ]);
        FbSource::upsert(
            $updates,
            ['source_id'],
            ['uid', 'source_type']
        );
        $this->output->success("done, total: " . count($updates));
        $this->sync();
    }

    private function getPage($content)
    {
        $regex = '/"userID"\s*:\s*"(\d+)"/';
        if (!preg_match($regex, $content, $matches)) {
            return null;
        }

        return $matches[1];
    }

    private function getGroup($content)
    {
        $regex = '/"group_id"\s*:\s*"(\d+)"/';
        if (!preg_match($regex, $content, $matches)) {
            return null;
        }
        return $matches[1];
    }

    private function isDie($content)
    {
        return str_contains($content, "isn't available");
    }

    private function headers()
    {
        return [
            'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language' => 'en-US,en;q=0.9,vi;q=0.8',
            'cache-control' => 'no-cache',
            'dpr' => '0.90625',
            'pragma' => 'no-cache',
            'priority' => 'u=0, i',
            'sec-ch-prefers-color-scheme' => 'dark',
            'sec-ch-ua' => '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-full-version-list' => '"Chromium";v="136.0.7103.113", "Google Chrome";v="136.0.7103.113", "Not.A/Brand";v="99.0.0.0"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-model' => '""',
            'sec-ch-ua-platform' => '"Linux"',
            'sec-ch-ua-platform-version' => '"6.11.0"',
            'sec-fetch-dest' => 'document',
            'sec-fetch-mode' => 'navigate',
            'sec-fetch-site' => 'same-origin',
            'sec-fetch-user' => '?1',
            'upgrade-insecure-requests' => '1',
            'user-agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'viewport-width' => '1508',
        ];
    }
}

