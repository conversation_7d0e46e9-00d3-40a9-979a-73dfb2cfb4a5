<?php

namespace App\Console\Commands;

use App\Models\Counter;
use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use App\Models\FbSource;
use App\Service\ApiReactionService;
use App\Service\FacebookToken;
use App\Service\FacebookToken2;
use App\Service\TelegramService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:tesst';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cookei = 'datr=w4I9aDP1OGjflL8Kdi5-5KeK; sb=w4I9aFIEiSi0wyhi-dE3q2Ya; ps_l=1; ps_n=1; dpr=0.90625; locale=en_US; wd=1508x679; c_user=61576034855793; xs=34%3AAzBMGekzGG56EA%3A2%3A1749001496%3A-1%3A-1; fr=1fPaCSSzRN3xfRls0.AWfvpHCIVzlU4Hu461ppfdQ9QnoOC-9T7efzunwjbO7UKuxWeuo.BoPYyx..AAA.0.0.BoP6Ud.AWfUd4Nd8uMhUKPvygRlDvN60x4';
        $token = new FacebookToken2($cookei);
        $t = $token->getToken();
        dd($t);
    }

    private function facebookSource()
    {
        $now = Carbon::now();
        $from = $now->copy()->subMinutes(15);
        $to = $now;

        // Tạo nội dung báo cáo
        $content = "<b>Facebook Source Report (last 15 min)</b>\n";

        // Số lượng source chưa check
        $countNoCheck = FbSource::whereNull('source_type')->count();
        $content .= "<code>Chưa check:</code> <b>{$countNoCheck}</b>\n";

        // Lấy các loại Counter cần thiết
        $counters = Counter::query()
            ->whereIn('type', [2, 4])
            ->whereBetween('created_at', [$from, $to])
            ->get()
            ->groupBy('type')
            ->map(fn($items) => $items->sum('count'));

        $checked = $counters[4] ?? 0;
        $synced = $counters[2] ?? 0;

        $content .= "<code>Đã check:</code> <b>{$checked}</b>\n";
        $content .= "<code>Đã sync:</code> <b>{$synced}</b>\n";

        TelegramService::sendMessage($content);
    }

}

