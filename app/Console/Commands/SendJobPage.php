<?php

namespace App\Console\Commands;

use App\Models\FacebookGroup;
use App\Models\FacebookPage;
use Illuminate\Console\Command;

class SendJobPage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sendjob-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $totalWorker = get_config('total_worker');

        FacebookPage::query()->where('is_priority',1)->chunkById($totalWorker, function ($groups) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_fb_pages', json_encode($groups));
        });
        FacebookPage::query()->where('is_priority',0)->chunkById($totalWorker, function ($groups) {
            \Illuminate\Support\Facades\Redis::rpush('jobs_queue_fb_pages', json_encode($groups));
        });
        $this->output->success("done, total " . FacebookGroup::count());
    }


}
