<?php

namespace App\Console\Commands;

use App\Models\FacebookPost;
use App\Models\FacebookUser;
use App\Service\TelegramService;
use Illuminate\Console\Command;

class SendJobUserPage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-job-users-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send job user page';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        $totalWorker = get_config('total_worker');
        $totalWorker = 100;

        FacebookUser::query()
            ->where('is_private',0)
            ->orderBy('curr_page','desc')
            ->orderBy('time_check', 'asc')
            ->chunkById($totalWorker, function ($groups) {
                \Illuminate\Support\Facades\Redis::rpush('jobs_queue_user_pages', json_encode($groups));
            });

        $count = FacebookUser::query()
//            ->where('status',1)
            ->where('is_private',0)
            ->count();

//        TelegramService::sendMessage("Send job comment: tổng {$count} users page");
        $this->output->success("done, total " . $count);
    }


}


