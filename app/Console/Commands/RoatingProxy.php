<?php

namespace App\Console\Commands;

use App\Models\Proxy;
use Illuminate\Console\Command;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Concurrency;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Redis;


class RoatingProxy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:roating-proxy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Xoay proxy';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (empty(get_config('cron_roating'))){
            return false;
        }
        $wwProxies = Proxy::query()->where('provider', 1)->get();
        Http::pool(
            fn($pool) => $wwProxies->map(fn(Proxy $proxy) => $pool->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => "application/json"
            ])->get($proxy["extra"]["url_rotating"])
            )
        );

        $tmpProxies = Proxy::query()->where('provider', 3)->get();
        $responses = Http::pool(
            fn($pool) => $tmpProxies->map(fn(Proxy $proxy) => $pool->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => "application/json"
            ])->get($proxy["extra"]["url_rotating"])
            )
        );
        $this->output->success("done");
        return 1;
    }
}
