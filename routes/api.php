<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::post('/sync-post',[\App\Http\Controllers\Api\PostController::class,'syncPost']);

Route::prefix('/reaction')->group(function () {
    Route::post('/facebook', [\App\Http\Controllers\Api\ReactionController::class, 'facebookRation']);
    Route::get('/batch/{id?}', [\App\Http\Controllers\Api\ReactionController::class, 'getReactionById']);
});
