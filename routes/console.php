<?php

use Illuminate\Support\Facades\Schedule;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

//Artisan::command('inspire', function () {
//    $this->comment(Inspiring::quote());
//})->purpose('Display an inspiring quote');

Schedule::command('app:roating-proxy')->everyThirtySeconds();
Schedule::command('app:set-proxy')->everyThirtySeconds();
Schedule::command('app:truncate')->monthlyOn(1, '00:00');
Schedule::command('app:sendjob')->everyThirtyMinutes();
Schedule::command('app:update-cursor')->everyThirtyMinutes();
Schedule::command('app:sendjob 1')->everyTenMinutes();
Schedule::command('app:sendjob-post')->everyTenMinutes();

Schedule::command('app:send-job-users-page')->everyFiveMinutes();
Schedule::command('app:convert-source check')->everyMinute()
    ->withoutOverlapping();
Schedule::command('app:update-via')->everyMinute()
    ->withoutOverlapping();

Schedule::command('app:convert-pfbid')
    ->everyMinute()
    ->withoutOverlapping();
//Schedule::command('app:sendjob-page')->everyFifteenMinutes();
Schedule::command('app:count')->everyFifteenMinutes();
