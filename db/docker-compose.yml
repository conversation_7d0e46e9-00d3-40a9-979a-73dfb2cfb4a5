version: '3.8'

services:
    db-container:
        image: debian:bullseye-slim
        container_name: db-container
        command: >
            bash -c "
              apt-get update && \
              apt-get install -y mariadb-server redis-server curl gnupg && \
              curl -fsSL https://www.mongodb.org/static/pgp/server-6.0.asc | tee /etc/apt/trusted.gpg.d/mongodb.asc && \
              echo 'deb [arch=amd64,arm64] https://repo.mongodb.org/apt/debian bullseye/mongodb-org/6.0 main' | tee /etc/apt/sources.list.d/mongodb-org-6.0.list && \
              apt-get update && \
              apt-get install -y mongodb-org && \
              echo 'requirepass Pewpew@11' >> /etc/redis/redis.conf && \
              sed -i 's/^bind .*/bind 0.0.0.0/' /etc/redis/redis.conf && \
              sed -i 's/^bind-address.*/bind-address = 0.0.0.0/' /etc/mysql/mariadb.conf.d/50-server.cnf && \
              service mariadb start && \
              service redis-server start && \
              mongod --fork --logpath /var/log/mongodb.log --bind_ip 127.0.0.1 && \
              sleep 5 && \
              mongosh --eval \"db.createUser({user: 'admin', pwd: 'Pewpew@11', roles: [{role: 'root', db: 'admin'}]})\" admin && \
              pkill mongod && \
              sleep 3 && \
              mongod --auth --fork --logpath /var/log/mongodb.log --bind_ip 0.0.0.0 && \
              tail -f /dev/null"
        ports:
            - "3309:3309"  # MariaDB
            - "6379:6379"  # Redis
            - "27017:27017" # MongoDB
        volumes:
            - mariadb-data:/var/lib/mysql
            - redis-data:/data
            - mongo-data:/data/db
        networks:
            - app_nw

    rabbitmq:
        image: rabbitmq:3-management
        container_name: rabbitmq-container
        environment:
            RABBITMQ_DEFAULT_USER: app_db
            RABBITMQ_DEFAULT_PASS: FraZtakjOyZDaEA
        ports:
            - "5672:5672"   # Cổng AMQP
            - "15672:15672" # RabbitMQ Management UI
        networks:
            - app_nw
        volumes:
            - rabbitmq-data:/var/lib/rabbitmq

networks:
    app_nw:
        driver: bridge

volumes:
    mariadb-data:
    redis-data:
    mongo-data:
    rabbitmq-data:
