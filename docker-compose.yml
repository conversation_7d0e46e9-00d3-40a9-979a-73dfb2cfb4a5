version: '3.8'

services:
    app:
        build: .
        container_name: laravel_app
        restart: unless-stopped
        working_dir: /var/www
        volumes:
            - .:/var/www
        networks:
            - laravel
    nginx:
        image: nginx:latest
        container_name: laravel_nginx
        restart: unless-stopped
        ports:
            - "8080:80"
        volumes:
            - .:/var/www
            - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
            - ./php.ini:/usr/local/etc/php/conf.d/custom.ini
        depends_on:
            - app
        networks:
            - laravel

networks:
    laravel:
        driver: bridge
