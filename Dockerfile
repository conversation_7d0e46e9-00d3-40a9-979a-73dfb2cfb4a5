# Sử dụng FrankenPHP với PHP 8.3
FROM dunglas/frankenphp:php8.3

# Cài đặt các package cần thiết
RUN apt-get update && apt-get install -y \
    git unzip libpq-dev libzip-dev \
    zip libpng-dev libjpeg-dev libfreetype6-dev \
    libonig-dev supervisor cron \
    libicu-dev && \
    docker-php-ext-configure intl && \
    docker-php-ext-install intl gd pdo pdo_mysql zip mbstring sockets pcntl && \
    pecl install redis && docker-php-ext-enable redis

# Thiết lập thư mục làm việc
WORKDIR /var/www

# Sao chép file vào container
COPY . .




## Cài đặt Laravel dependencies
#RUN composer install --no-dev --optimize-autoloader --no-interaction

# Phân quyền thư mục
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache

# Sao chép file cấu hình Supervisor
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Mở cổng 9001 cho FrankenPHP
EXPOSE 9001

# Tạo file cấu hình FrankenPHP
RUN echo 'SERVER_NAME=:9001' > /etc/environment

# Chạy FrankenPHP, Supervisor và Cron
CMD ["sh", "-c", "cron & supervisord & frankenphp run --listen :9001"]
