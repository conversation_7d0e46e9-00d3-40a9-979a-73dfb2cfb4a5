<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'mysql';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fb_comments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fb_post_id')->index();
            $table->json('data')->nullable();
            $table->string('comment_id')->index();
//            $table->string('fb_comment_id')->index();
            $table->integer('created_time')->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fb_comments');
    }
};
