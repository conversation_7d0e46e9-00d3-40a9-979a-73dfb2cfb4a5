<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fb_posts', function (Blueprint $table) {
            $table->tinyInteger('type_get')->default(0);
            $table->integer('curr_page')->default(0);
            $table->integer('time_check')->default(0);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fb_posts', function (Blueprint $table) {
            //
        });
    }
};
